<template>
  <q-dialog
    :model-value="modelValue"
    @update:model-value="updateModelValue"
    persistent
    maximized
    transition-show="slide-up"
    transition-hide="slide-down"
    class="full-screen-dialog"
  >
    <div class="full-screen-dialog-container">
      <!-- Background with glassmorphism effect -->
      <div class="full-screen-dialog-backdrop" />
      
      <!-- Main content area -->
      <div class="full-screen-dialog-content">
        <!-- Header -->
        <div class="full-screen-dialog-header">
          <div class="header-content">
            <div class="header-left">
              <slot name="header-left">
                <div class="dialog-title">
                  {{ title }}
                </div>
                <div v-if="subtitle" class="dialog-subtitle">
                  {{ subtitle }}
                </div>
              </slot>
            </div>
            
            <div class="header-right">
              <slot name="header-right">
                <q-btn
                  flat
                  round
                  dense
                  icon="close"
                  color="white"
                  @click="closeDialog"
                  class="close-button"
                  aria-label="Close dialog"
                />
              </slot>
            </div>
          </div>
        </div>

        <!-- Body -->
        <div class="full-screen-dialog-body">
          <div class="body-content">
            <slot />
          </div>
        </div>

        <!-- Footer -->
        <div v-if="$slots.footer" class="full-screen-dialog-footer">
          <div class="footer-content">
            <slot name="footer" />
          </div>
        </div>
      </div>
    </div>
  </q-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  modelValue: boolean
  title?: string
  subtitle?: string
  showCloseButton?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: '',
  subtitle: '',
  showCloseButton: true
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'close': []
}>()

const updateModelValue = (value: boolean) => {
  emit('update:modelValue', value)
}

const closeDialog = () => {
  emit('close')
  emit('update:modelValue', false)
}
</script>

<style scoped>
.full-screen-dialog {
  z-index: 9000;
}

.full-screen-dialog-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.full-screen-dialog-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  z-index: 1;
}

.full-screen-dialog-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.full-screen-dialog-header {
  flex-shrink: 0;
  background: linear-gradient(
    135deg,
    rgba(13, 138, 62, 0.9) 0%,
    rgba(13, 138, 62, 0.8) 100%
  );
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 1.5rem 2rem;
  color: white;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.header-left {
  flex: 1;
}

.dialog-title {
  font-size: 1.75rem;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 0.25rem;
}

.dialog-subtitle {
  font-size: 1rem;
  opacity: 0.9;
  font-weight: 400;
}

.header-right {
  flex-shrink: 0;
  margin-left: 1rem;
}

.close-button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.full-screen-dialog-body {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.02);
}

.body-content {
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.full-screen-dialog-footer {
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding: 1.5rem 2rem;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .full-screen-dialog-header {
    padding: 1rem 1.5rem;
  }
  
  .dialog-title {
    font-size: 1.5rem;
  }
  
  .dialog-subtitle {
    font-size: 0.9rem;
  }
  
  .full-screen-dialog-body {
    padding: 1.5rem;
  }
  
  .full-screen-dialog-footer {
    padding: 1rem 1.5rem;
  }
  
  .footer-content {
    flex-direction: column;
    gap: 0.75rem;
  }
}

@media (max-width: 480px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .header-right {
    align-self: flex-end;
    margin-left: 0;
  }
  
  .dialog-title {
    font-size: 1.25rem;
  }
  
  .full-screen-dialog-body {
    padding: 1rem;
  }
}

/* Smooth transitions */
.full-screen-dialog-container {
  transition: all 0.3s ease;
}

.close-button,
.full-screen-dialog-header,
.full-screen-dialog-body,
.full-screen-dialog-footer {
  transition: all 0.3s ease;
}

/* Accessibility */
.close-button:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
  .full-screen-dialog-content {
    background: rgba(30, 30, 30, 0.95);
  }
  
  .full-screen-dialog-footer {
    background: rgba(30, 30, 30, 0.95);
    border-top-color: rgba(255, 255, 255, 0.1);
  }
}
</style>
