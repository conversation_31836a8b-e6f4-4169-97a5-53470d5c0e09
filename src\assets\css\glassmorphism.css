/**
 * Glassmorphism Design System
 * 
 * Provides reusable glassmorphism styles for the multi-step registration flow
 * and other components that need modern glass-like effects.
 */

/* Base glassmorphism variables */
:root {
  --glass-bg-light: rgba(255, 255, 255, 0.1);
  --glass-bg-medium: rgba(255, 255, 255, 0.15);
  --glass-bg-heavy: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-border-hover: rgba(255, 255, 255, 0.3);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  --glass-shadow-hover: 0 12px 40px rgba(0, 0, 0, 0.15);
  --glass-blur: blur(16px);
  --glass-blur-heavy: blur(24px);
  
  /* Primary color glassmorphism variants */
  --glass-primary-bg: rgba(13, 138, 62, 0.1);
  --glass-primary-border: rgba(13, 138, 62, 0.2);
  --glass-primary-hover: rgba(13, 138, 62, 0.15);
  
  /* Secondary color glassmorphism variants */
  --glass-secondary-bg: rgba(116, 181, 36, 0.1);
  --glass-secondary-border: rgba(116, 181, 36, 0.2);
  --glass-secondary-hover: rgba(116, 181, 36, 0.15);
}

/* Base glassmorphism card */
.glass-card {
  background: var(--glass-bg-light);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  box-shadow: var(--glass-shadow);
  transition: all 0.3s ease;
}

.glass-card:hover {
  background: var(--glass-bg-medium);
  border-color: var(--glass-border-hover);
  box-shadow: var(--glass-shadow-hover);
  transform: translateY(-2px);
}

/* Glass card variants */
.glass-card--medium {
  background: var(--glass-bg-medium);
  backdrop-filter: var(--glass-blur-heavy);
  -webkit-backdrop-filter: var(--glass-blur-heavy);
}

.glass-card--heavy {
  background: var(--glass-bg-heavy);
  backdrop-filter: var(--glass-blur-heavy);
  -webkit-backdrop-filter: var(--glass-blur-heavy);
}

/* Primary themed glass card */
.glass-card--primary {
  background: var(--glass-primary-bg);
  border-color: var(--glass-primary-border);
}

.glass-card--primary:hover {
  background: var(--glass-primary-hover);
  border-color: var(--glass-primary-border);
}

/* Secondary themed glass card */
.glass-card--secondary {
  background: var(--glass-secondary-bg);
  border-color: var(--glass-secondary-border);
}

.glass-card--secondary:hover {
  background: var(--glass-secondary-hover);
  border-color: var(--glass-secondary-border);
}

/* Interactive glass card (for selections) */
.glass-card--interactive {
  cursor: pointer;
  user-select: none;
  position: relative;
  overflow: hidden;
}

.glass-card--interactive::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.glass-card--interactive:hover::before {
  opacity: 1;
}

.glass-card--interactive:active {
  transform: translateY(0) scale(0.98);
}

/* Selected state for interactive cards */
.glass-card--selected {
  background: var(--glass-primary-hover);
  border-color: var(--glass-primary-border);
  box-shadow: 0 0 0 2px rgba(13, 138, 62, 0.3), var(--glass-shadow-hover);
}

.glass-card--selected::before {
  opacity: 1;
  background: linear-gradient(
    135deg,
    rgba(13, 138, 62, 0.1) 0%,
    rgba(13, 138, 62, 0.05) 100%
  );
}

/* Glass button styles */
.glass-btn {
  background: var(--glass-bg-light);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: inherit;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  text-decoration: none;
}

.glass-btn:hover {
  background: var(--glass-bg-medium);
  border-color: var(--glass-border-hover);
  transform: translateY(-1px);
  box-shadow: var(--glass-shadow);
}

.glass-btn:active {
  transform: translateY(0);
}

.glass-btn--primary {
  background: var(--glass-primary-bg);
  border-color: var(--glass-primary-border);
  color: #0D8A3E;
}

.glass-btn--primary:hover {
  background: var(--glass-primary-hover);
  color: #0D8A3E;
}

/* Glass input styles */
.glass-input {
  background: var(--glass-bg-light);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  padding: 0.75rem 1rem;
  color: inherit;
  transition: all 0.3s ease;
}

.glass-input:focus {
  outline: none;
  background: var(--glass-bg-medium);
  border-color: var(--glass-primary-border);
  box-shadow: 0 0 0 2px rgba(13, 138, 62, 0.2);
}

.glass-input::placeholder {
  color: rgba(0, 0, 0, 0.5);
}

/* Glass container for layouts */
.glass-container {
  background: var(--glass-bg-light);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: var(--glass-shadow);
}

/* Category selection specific styles */
.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  padding: 1rem 0;
}

.category-card {
  padding: 2rem 1.5rem;
  text-align: center;
  min-height: 160px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.category-card__icon {
  font-size: 2.5rem;
  color: #0D8A3E;
  margin-bottom: 0.5rem;
}

.category-card__title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 0.5rem;
}

.category-card__description {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.4;
  text-align: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .category-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .category-card {
    padding: 1.5rem 1rem;
    min-height: 140px;
  }
  
  .category-card__icon {
    font-size: 2rem;
  }
  
  .category-card__title {
    font-size: 1.1rem;
  }
  
  .category-card__description {
    font-size: 0.85rem;
  }
  
  .glass-container {
    padding: 1.5rem;
    border-radius: 16px;
  }
}

@media (max-width: 480px) {
  .category-grid {
    grid-template-columns: 1fr;
  }
  
  .category-card {
    padding: 1rem;
    min-height: 120px;
  }
  
  .glass-container {
    padding: 1rem;
    border-radius: 12px;
  }
}

/* Animation utilities */
.glass-fade-in {
  animation: glassFadeIn 0.5s ease-out;
}

.glass-slide-up {
  animation: glassSlideUp 0.5s ease-out;
}

@keyframes glassFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes glassSlideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Accessibility improvements */
.glass-card--interactive:focus {
  outline: 2px solid #0D8A3E;
  outline-offset: 2px;
}

.glass-btn:focus {
  outline: 2px solid #0D8A3E;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .glass-card,
  .glass-btn,
  .glass-input,
  .glass-container {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(0, 0, 0, 0.3);
  }
  
  .glass-card--selected {
    background: rgba(13, 138, 62, 0.2);
    border-color: #0D8A3E;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .glass-card,
  .glass-btn,
  .glass-input,
  .glass-card--interactive,
  .glass-card--interactive::before {
    transition: none;
  }
  
  .glass-fade-in,
  .glass-slide-up {
    animation: none;
  }
}
