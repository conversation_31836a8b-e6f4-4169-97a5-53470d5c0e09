/* Import ZB Innovation Button System */
@import './assets/css/button-system.css';

/* Import Glassmorphism Design System */
@import './assets/css/glassmorphism.css';

:root {
  font-family: 'Rubik', sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Enforce Rubik font throughout the application */
*, *::before, *::after,
.q-page, .q-layout, .q-header, .q-footer,
.q-item, .q-btn, input, textarea, select {
  font-family: 'Rubik', sans-serif !important;
}

html, body {
  margin: 0;
  padding: 0;
  min-width: 320px;
  min-height: 100vh;
  overflow-x: hidden !important;
  max-width: 100vw;
  width: 100%;
  position: relative;
}

/* Ensure all menus don't cause horizontal scrolling */
.q-menu {
  overflow-x: hidden !important;
}

/* Smile-Factory specific colors */
.zb-primary {
  color: #245926;
}

.zb-secondary {
  color: #74b524;
}

.zb-accent {
  color: #072a13;
}

.bg-zb-primary {
  background-color: #245926;
}

.bg-zb-secondary {
  background-color: #74b524;
}

.bg-zb-accent {
  background-color: #072a13;
}

.text-white {
  color: white;
}
