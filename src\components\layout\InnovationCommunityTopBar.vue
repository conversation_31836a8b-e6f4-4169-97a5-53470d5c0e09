<template>
  <q-toolbar class="innovation-community-toolbar">
    <!-- Left Section: Hamburger Menu and Logo -->
    <div class="toolbar-left">
      <q-btn
        flat
        dense
        round
        icon="menu"
        aria-label="Menu"
        class="hamburger-btn"
        @click.stop="handleHamburgerClick"
        style="background-color: rgba(255, 0, 0, 0.1); border: 1px solid red;"
      >
        <q-icon name="menu" size="24px" />
        <q-tooltip>Click to toggle menu</q-tooltip>
      </q-btn>

      <!-- Logo next to hamburger menu -->
      <router-link to="/" class="logo-link">
        <img
          src="/smile-factory-logo.svg"
          alt="Smile Factory Logo"
          class="toolbar-logo"
        />
      </router-link>
    </div>

    <!-- Center Section: Navigation -->
    <div class="toolbar-center">

      <!-- Quick Navigation Dropdown (moved to center) -->
      <div class="quick-nav-container desktop-only pulse-animation">
        <q-select
          v-model="selectedTab"
          :options="tabOptions"
          filled
          dense
          emit-value
          map-options
          @update:model-value="handleTabChange"
          class="quick-nav-select"
          dropdown-icon="arrow_drop_down"
        >
          <template v-slot:prepend>
            <q-icon :name="getTabIcon(selectedTab)" class="nav-icon" />
          </template>
          <template v-slot:after>
            <q-badge
              v-if="getTabNotificationCount(selectedTab) > 0"
              color="red"
              floating
              rounded
              :label="getTabNotificationCount(selectedTab)"
            />
          </template>
        </q-select>

        <!-- Notification Badge for Dropdown Visibility -->
        <q-badge
          color="accent"
          text-color="white"
          floating
          rounded
          label="NEW"
          class="dropdown-visibility-badge"
        />
      </div>
    </div>

    <!-- Right Section: Actions -->
    <div class="toolbar-actions">
      <!-- Create Post Button -->
      <!-- Desktop: Button with text -->
      <q-btn
        unelevated
        rounded
        color="primary"
        icon="add"
        label="Create"
        class="create-btn desktop-only"
        @click="$emit('open-create')"
      />

      <!-- Mobile: Icon only -->
      <q-btn
        flat
        dense
        round
        icon="add"
        class="create-btn mobile-only mobile-create-icon"
        @click="$emit('open-create')"
      />

      <!-- Notifications -->
      <q-btn
        flat
        dense
        round
        class="notification-btn"
        @click.stop="toggleNotifications"
      >
        <q-icon name="notifications" />
        <q-badge
          v-if="notificationCount > 0"
          color="red"
          floating
          rounded
          :label="notificationCount"
        />
      </q-btn>



      <!-- Profile Menu - Only show when authenticated -->
      <q-btn-dropdown
        v-if="authStore.isAuthenticated"
        flat
        dense
        round
        class="profile-btn"
        dropdown-icon="none"
      >
        <template v-slot:label>
          <q-avatar size="32px">
            <img
              v-if="userAvatar"
              :src="userAvatar"
              alt="User Avatar"
            />
            <q-icon
              v-else
              name="person"
              color="grey-6"
            />
          </q-avatar>
        </template>

        <q-list>
          <q-item clickable v-close-popup @click="goToProfile">
            <q-item-section avatar>
              <q-icon name="person" />
            </q-item-section>
            <q-item-section>Profile</q-item-section>
          </q-item>

          <q-item clickable v-close-popup @click="goToDashboard">
            <q-item-section avatar>
              <q-icon name="dashboard" />
            </q-item-section>
            <q-item-section>Dashboard</q-item-section>
          </q-item>

          <q-item clickable v-close-popup @click="goToSettings">
            <q-item-section avatar>
              <q-icon name="settings" />
            </q-item-section>
            <q-item-section>Settings</q-item-section>
          </q-item>

          <q-separator />

          <q-item clickable v-close-popup @click="signOut">
            <q-item-section avatar>
              <q-icon name="logout" />
            </q-item-section>
            <q-item-section>Sign Out</q-item-section>
          </q-item>
        </q-list>
      </q-btn-dropdown>

      <!-- Sign In Button - Only show when not authenticated -->
      <q-btn
        v-if="!authStore.isAuthenticated"
        unelevated
        rounded
        color="primary"
        label="Sign In"
        class="signin-btn"
        @click="goToSignIn"
      />
    </div>

    <!-- Notifications Dropdown -->
    <q-menu
      v-model="showNotifications"
      anchor="bottom right"
      self="top right"
      class="notifications-menu"
    >
      <q-card style="width: 300px; max-height: 400px;">
        <q-card-section class="q-pb-none">
          <div class="text-h6">Notifications</div>
        </q-card-section>

        <q-separator />

        <q-card-section class="q-pa-none">
          <q-list>
            <q-item
              v-for="notification in notifications"
              :key="notification.id"
              clickable
              v-ripple
              @click="handleNotificationClick(notification)"
            >
              <q-item-section avatar>
                <q-icon :name="notification.icon" :color="notification.color" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ notification.title }}</q-item-label>
                <q-item-label caption>{{ notification.message }}</q-item-label>
                <q-item-label caption>{{ notification.time }}</q-item-label>
              </q-item-section>
            </q-item>

            <q-item v-if="notifications.length === 0">
              <q-item-section class="text-center text-grey-6">
                No new notifications
              </q-item-section>
            </q-item>
          </q-list>
        </q-card-section>

        <q-separator />

        <q-card-actions align="center">
          <q-btn
            flat
            label="View All"
            @click="viewAllNotifications"
          />
        </q-card-actions>
      </q-card>
    </q-menu>
  </q-toolbar>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// Props
const props = defineProps<{
  activeTab: string
}>()

// Emits
const emit = defineEmits<{
  'toggle-left-drawer': []
  'open-create': []
  'trigger-ai': []
  'tab-change': [tab: string]
}>()

// State
const showNotifications = ref(false)
const notifications = ref([
  {
    id: 1,
    title: 'New Connection Request',
    message: 'John Doe wants to connect with you',
    time: '2 minutes ago',
    icon: 'person_add',
    color: 'blue'
  },
  {
    id: 2,
    title: 'Event Reminder',
    message: 'Innovation Workshop starts in 1 hour',
    time: '1 hour ago',
    icon: 'event',
    color: 'orange'
  }
])

// Computed
const selectedTab = computed({
  get: () => props.activeTab,
  set: (value) => emit('tab-change', value)
})

const tabOptions = computed(() => [
  { label: 'Feed', value: 'feed', icon: 'home' },
  { label: 'Profiles', value: 'profiles', icon: 'people' },
  { label: 'Events', value: 'events', icon: 'event' },
  { label: 'Blog', value: 'blog', icon: 'article' },
  { label: 'Marketplace', value: 'marketplace', icon: 'storefront' }
])

const notificationCount = computed(() => notifications.value.length)

const userAvatar = computed(() => {
  return authStore.user?.avatar || null
})

// Methods
function handleHamburgerClick(event: Event) {
  console.log('🍔 Hamburger menu clicked!', event)

  // Prevent event propagation to avoid conflicts
  event.preventDefault()
  event.stopPropagation()

  // Close notifications if open
  showNotifications.value = false

  // Emit the toggle drawer event
  emit('toggle-left-drawer')

  console.log('🍔 Hamburger menu clicked - toggling drawer')
}

function handleTabChange(tab: string) {
  emit('tab-change', tab)
}

function getTabIcon(tab: string) {
  const option = tabOptions.value.find(opt => opt.value === tab)
  return option?.icon || 'home'
}

function getTabNotificationCount(tab: string) {
  // Return notification count for specific tab
  return 0
}

function toggleNotifications() {
  showNotifications.value = !showNotifications.value
}

function handleNotificationClick(notification: any) {
  console.log('Notification clicked:', notification)
  showNotifications.value = false
}

function viewAllNotifications() {
  router.push('/dashboard/notifications')
  showNotifications.value = false
}

function goToProfile() {
  router.push('/dashboard/profile')
}

function goToDashboard() {
  router.push('/dashboard')
}

function goToSettings() {
  router.push('/dashboard/settings')
}

function signOut() {
  authStore.signOut()
  router.push('/')
}

function goToSignIn() {
  router.push('/sign-in')
}
</script>

<style scoped>
.innovation-community-toolbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 0 16px;
  min-height: 64px;
}

.toolbar-left {
  display: flex !important;
  align-items: center;
  gap: 12px;
  position: relative;
  z-index: 999;
  visibility: visible !important;
  opacity: 1 !important;
}

.toolbar-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.toolbar-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-link {
  display: flex;
  align-items: center;
  text-decoration: none;
}

.toolbar-logo {
  height: 32px;
  width: auto;
}

.quick-nav-container {
  position: relative;
}

.quick-nav-select {
  min-width: 200px;
}

.dropdown-visibility-badge {
  top: -8px;
  right: -8px;
  font-size: 10px;
  padding: 2px 6px;
}

.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.create-btn {
  font-weight: 600;
}

.mobile-create-icon {
  background: #0d8a3e;
  color: white;
}

.hamburger-btn {
  color: #0d8a3e !important;
  margin-right: 8px;
  position: relative;
  z-index: 1000;
  pointer-events: auto;
  min-width: 40px !important;
  min-height: 40px !important;
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.hamburger-btn:hover {
  background-color: rgba(13, 138, 62, 0.1);
}

.notification-btn {
  position: relative;
  color: #666;
}

.notification-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.signin-btn {
  background-color: #0d8a3e;
  color: white;
  font-weight: 600;
  padding: 8px 16px;
}

.profile-btn {
  margin-left: 8px;
}

.notifications-menu {
  margin-top: 8px;
  z-index: 2000;
}

.desktop-only {
  display: flex;
}

.mobile-only {
  display: none;
}

@media (max-width: 768px) {
  .desktop-only {
    display: none;
  }
  
  .mobile-only {
    display: flex;
  }
  
  .toolbar-center {
    display: none;
  }
  
  .innovation-community-toolbar {
    padding: 0 12px;
  }
}
</style>
