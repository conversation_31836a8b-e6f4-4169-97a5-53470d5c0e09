<template>
  <q-dialog v-model="isOpen" persistent>
    <q-card class="auth-dialog onboarding-dialog" style="min-width: 450px; max-width: 500px">
      <!-- Header -->
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">Welcome to the Platform!</div>
        <q-space />
        <q-btn flat round dense @click="handleClose">
          <q-icon name="close" />
        </q-btn>
      </q-card-section>

      <!-- Content -->
      <q-card-section class="q-pt-sm">
        <!-- Success Section -->
        <div class="success-section">
          <div class="success-icon">
            <unified-icon name="check_circle" size="3rem" color="primary" />
          </div>
          <h3 class="success-title">Account Created Successfully!</h3>
          <p class="success-message">
            Welcome! You're now part of our community as a
            <strong>{{ selectedCategoryLabel }}</strong>.
          </p>
        </div>

        <!-- Next Steps Section -->
        <div class="next-steps-section">
          <h4 class="section-title">What would you like to do next?</h4>

          <div class="options-list">
            <!-- Continue with Profile Creation -->
            <q-btn
              unelevated
              no-caps
              color="primary"
              icon="person_add"
              label="Complete Profile"
              @click="handleContinueWithProfile"
              class="full-width q-mb-sm option-btn"
            />

            <q-btn
              flat
              no-caps
              color="grey-7"
              icon="dashboard"
              label="Explore Dashboard"
              @click="handleDoItLater"
              class="full-width option-btn"
            />
          </div>

          <div class="benefits-note">
            <unified-icon name="info" size="1rem" color="primary" />
            <span>Complete your profile to get better connections and opportunities</span>
          </div>
        </div>
      </q-card-section>

    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'

import UnifiedIcon from '@/components/ui/UnifiedIcon.vue'
import { useUnifiedAuth } from '@/services/unifiedAuthService'
import { useNotificationStore } from '@/stores/notifications'
import { useCategoryService } from '@/services/categoryService'

const router = useRouter()
const notifications = useNotificationStore()
const { state, closeAllDialogs } = useUnifiedAuth()
const { getCategoryById, getProfileTypeForCategory, clearSelectedCategory } = useCategoryService()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'close': []
}>()

// Computed properties
const isOpen = computed(() => state.value.isPostRegistrationOnboardingOpen)

const selectedCategoryLabel = computed(() => {
  const category = state.value.selectedCategory
  return category?.label || 'Member'
})

// Methods
const updateModelValue = (value: boolean) => {
  emit('update:modelValue', value)
}

const handleClose = () => {
  closeAllDialogs()
  emit('close')
}

const handleContinueWithProfile = async () => {
  try {
    const category = state.value.selectedCategory
    if (category) {
      // Navigate to profile creation with pre-selected category
      const profileType = getProfileTypeForCategory(category.id)
      
      closeAllDialogs()
      
      // Navigate to profile creation page
      await router.push({
        name: 'profile-create',
        query: {
          type: profileType,
          category: category.id
        }
      })
      
      notifications.success('Let\'s complete your profile to get the most out of the platform!')
    } else {
      // Fallback if no category is selected
      closeAllDialogs()
      await router.push({ name: 'profile-create' })
    }
  } catch (error) {
    console.error('Error navigating to profile creation:', error)
    notifications.error('Something went wrong. Please try again.')
  }
}

const handleDoItLater = async () => {
  try {
    closeAllDialogs()
    
    // Clear the selected category since user chose to skip
    clearSelectedCategory()
    
    // Navigate to dashboard
    await router.push('/dashboard')
    
    // Show success notification
    notifications.success('Welcome to the platform! You can complete your profile anytime from your dashboard.')
  } catch (error) {
    console.error('Error navigating to dashboard:', error)
    notifications.error('Something went wrong. Please try again.')
  }
}
</script>

<style scoped>
.onboarding-dialog {
  border-radius: 12px;
}

.success-section {
  text-align: center;
  margin-bottom: 1.5rem;
}

.success-icon {
  margin-bottom: 1rem;
}

.success-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 0.75rem;
  line-height: 1.2;
}

.success-message {
  font-size: 0.95rem;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

.next-steps-section {
  margin-bottom: 1rem;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 1rem;
  text-align: center;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.option-btn {
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-weight: 500;
}

.benefits-note {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: #666;
  text-align: center;
  justify-content: center;
  margin-top: 0.75rem;
}



/* Mobile responsiveness */
@media (max-width: 480px) {
  .onboarding-dialog {
    min-width: 320px;
    max-width: 95vw;
    margin: 1rem;
  }

  .success-title {
    font-size: 1.25rem;
  }

  .success-message {
    font-size: 0.875rem;
  }

  .section-title {
    font-size: 1rem;
  }

  .benefits-note {
    font-size: 0.75rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .success-title {
    color: #f0f0f0;
  }

  .success-message {
    color: #ccc;
  }

  .section-title {
    color: #f0f0f0;
  }

  .benefits-note {
    color: #ccc;
  }
  
  .benefits-section {
    background: rgba(0, 0, 0, 0.1);
  }
}
</style>
